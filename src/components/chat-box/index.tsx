import React, { useState, useRef, useEffect } from 'react';
import { ChatBoxProps } from './types';
import Textarea from '@/components/textarea';
import Button from '@/components/button';
import { ButtonVariant } from '@/components/button/types';
import { PaperAirplaneIcon, HandThumbUpIcon, HandThumbDownIcon, ArrowPathIcon, ArrowUpTrayIcon } from '@heroicons/react/24/outline';
import { Controller, useForm } from 'react-hook-form';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

const ChatBox: React.FC<ChatBoxProps> = ({
  messages,
  onSend,
  isLoading,
  placeholder = 'Type your message...',
  onLike,
  onDislike,
  onRegenerate,
  onOpen,
}) => {
  const tabsConstants = useTranslations('I2R.homepage.tabs');
  const { control, handleSubmit, reset, watch } = useForm({
    defaultValues: {
      message: ''
    }
  });
  const homepageConstants = useTranslations('I2R.homepage');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Watch the form field value
  const messageValue = watch('message');

  useEffect(() => {
    if (messagesEndRef.current && typeof messagesEndRef.current.scrollIntoView === 'function') {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  const handleSend = (data: { message: string }) => {
    if (data.message.trim()) {
      onSend(data.message.trim());
      reset(); // Reset the form after sending
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(handleSend)();
    }
  };

  return (
    <div className="flex flex-col gap-4 h-full">
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 bg-gray-50 rounded-t-xl">
        {messages.map((msg) => (
          <div
            key={msg.id}
            className={`mb-4 flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            {msg.sender === 'user' ? (
              <div className="bg-gray-100 text-right rounded-xl p-4 max-w-xs ml-auto">
                <span className="text-gray-900 font-medium text-base">{msg.text}</span>
              </div>
            ) : (
              <div className="bg-white shadow-lg rounded-xl p-5 max-w-lg w-full">
                <div className="text-gray-800 font-medium text-base">
                  {msg.text}
                </div>
                {msg.showActions && (
                  <div className="flex gap-2 mt-4">
                    <Button
                      variant={ButtonVariant.FLAT}
                      className="flex items-center gap-2 border border-gray-200 rounded-lg px-3 py-1 hover:bg-gray-100"
                      startIcon={<HandThumbUpIcon width={20} height={20} />}
                      onClick={() => onLike && onLike(msg.id)}
                    >
                      <span className="text-sm font-medium">Like</span>
                    </Button>
                    <Button
                      variant={ButtonVariant.FLAT}
                      className="flex items-center gap-2 border border-gray-200 rounded-lg px-3 py-1 hover:bg-gray-100"
                      startIcon={<HandThumbDownIcon width={20} height={20} />}
                      onClick={() => onDislike && onDislike(msg.id)}
                    >
                      <span className="text-sm font-medium">Dislike</span>
                    </Button>
                    <Button
                      variant={ButtonVariant.FLAT}
                      className="flex items-center gap-2 border border-gray-200 rounded-lg px-3 py-1 hover:bg-gray-100"
                      startIcon={<ArrowPathIcon width={20} height={20} />}
                      onClick={() => onRegenerate && onRegenerate(msg.id)}
                    >
                      <span className="text-sm font-medium">Regenerate</span>
                    </Button>
                    <Button
                      variant={ButtonVariant.FLAT}
                      className="flex items-center gap-2 border border-gray-200 rounded-lg px-3 py-1 hover:bg-gray-100"
                      startIcon={
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="h-5 w-5 text-gray-500"><path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h6m2 4.5V6.75A2.25 2.25 0 0 0 17.75 4.5H6.25A2.25 2.25 0 0 0 4 6.75v10.5A2.25 2.25 0 0 0 6.25 19.5h7.5" /></svg>
                      }
                      onClick={() => onOpen && onOpen(msg.id)}
                    >
                      <span className="text-sm font-medium">Open</span>
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
      <div className="flex gap-4">
        {/* PRD Button */}
      
          <Button
            variant={ButtonVariant.GHOST}
            className="border-secondary-neutral-200 px-5"
            // onClick={() => setSelectedTab(tabsConstants('prd'))}
            // isDisabled={isPrdDisabled}
          >
            {tabsConstants('prd')}
          </Button>
        {/* Epics Button */}
        <Button
          variant={ButtonVariant.GHOST}
          className="border-secondary-neutral-200 px-5"
          // onClick={() => setSelectedTab(tabsConstants('epics'))}
          // isDisabled={isEpicsAndUserStoriesDisabled}

        >
          {tabsConstants('epics')}
        </Button>
        {/* Stories Button */}
        <Button
          variant={ButtonVariant.GHOST}
          className="border-secondary-neutral-200 px-5"
          // onClick={() => setSelectedTab(tabsConstants('stories'))}
          // isDisabled={isEpicsAndUserStoriesDisabled}
        >
          {tabsConstants('stories')}
        </Button>
      </div>
      {/* Input */}
      <div className="flex items-end rounded-xl border border-gray-200 bg-white">
        <Controller
          name="message"
          control={control}
          rules={{ required: 'Message is required' }}
          render={({ field }) => (
            <div className="relative w-full">
              <Textarea
                {...field}
                placeholder={placeholder}
                className="rounded-xl border-none w-full resize-none focus:ring-0"
                onKeyDown={handleKeyDown}
                onKeyUp={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSubmit(handleSend)();
                  }
                }}
              />
              {/* Left icons */}
              <div className="absolute left-4 top-1/2 -translate-y-1/2 flex items-center gap-2">
                <Image src="/icons/jira.svg" alt="jira logo" width={20} height={20} />
                <ArrowUpTrayIcon className="h-6 w-6 text-primary-teal-600" />
              </div>
              {/* Right icon */}
              <div className="absolute right-4 top-1/2 -translate-y-1/2 flex items-center">
                <PaperAirplaneIcon
                  className={`h-6 w-6 cursor-pointer ${messageValue?.trim() ? 'text-primary-teal-600' : 'text-secondary-neutral-400'}`}
                  onClick={() => handleSubmit(handleSend)()}
                />
              </div>
            </div>
          )}
        />
      </div>
    </div>
  );
};

export default ChatBox; 