export interface ChatMessage {
  id: string;
  sender: 'user' | 'ai' | 'system';
  text: string;
  timestamp?: string;
  showActions?: boolean; // Show like/dislike/regenerate/open buttons
}

export interface ChatBoxProps {
  messages: ChatMessage[];
  onSend: (message: string) => void;
  isLoading?: boolean;
  placeholder?: string;
  onLike?: (id: string) => void;
  onDislike?: (id: string) => void;
  onRegenerate?: (id: string) => void;
  onOpen?: (id: string) => void;
} 