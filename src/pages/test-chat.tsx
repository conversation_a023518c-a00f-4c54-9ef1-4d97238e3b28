import React, { useState } from 'react';
import { NextIntlClientProvider } from 'next-intl';
import ChatBox from '@/components/chat-box';
import { ChatMessage } from '@/components/chat-box/types';

// Mock messages for testing
const messages = {
  'I2R': {
    'homepage': {
      'inputs': {
        'idea': {
          'placeholder': 'Type your message...',
          'error': 'Message is required'
        }
      }
    }
  }
};

const TestChatPage = () => {
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      sender: 'user',
      text: 'Hello, this is a test message!'
    },
    {
      id: '2',
      sender: 'ai',
      text: 'Hi there! I received your message. This is an AI response.',
      showActions: true
    }
  ]);

  const handleSend = (message: string) => {
    console.log('Message sent:', message);
    
    // Add user message
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      sender: 'user',
      text: message
    };

    // Add AI response
    const aiMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      sender: 'ai',
      text: `You said: "${message}". This is an automated response.`,
      showActions: true
    };

    setChatMessages(prev => [...prev, userMessage, aiMessage]);
  };

  const handleLike = (id: string) => {
    console.log('Liked message:', id);
    alert(`Liked message: ${id}`);
  };

  const handleDislike = (id: string) => {
    console.log('Disliked message:', id);
    alert(`Disliked message: ${id}`);
  };

  const handleRegenerate = (id: string) => {
    console.log('Regenerate message:', id);
    alert(`Regenerate message: ${id}`);
  };

  const handleOpen = (id: string) => {
    console.log('Open message:', id);
    alert(`Open message: ${id}`);
  };

  return (
    <NextIntlClientProvider locale="en" messages={messages}>
      <div className="min-h-screen bg-gray-100 p-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold mb-8 text-center">ChatBox Test Page</h1>
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="h-96">
              <ChatBox
                messages={chatMessages}
                onSend={handleSend}
                placeholder="Type your test message here..."
                onLike={handleLike}
                onDislike={handleDislike}
                onRegenerate={handleRegenerate}
                onOpen={handleOpen}
              />
            </div>
          </div>
          <div className="mt-8 p-4 bg-blue-50 rounded-lg">
            <h2 className="text-lg font-semibold mb-2">Test Instructions:</h2>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>Type a message in the input field and press Enter or click the send button</li>
              <li>Check the browser console for "Message sent:" logs</li>
              <li>Verify that the message appears in the chat</li>
              <li>Try clicking the action buttons (Like, Dislike, Regenerate, Open) on AI messages</li>
              <li>Test that empty messages are not sent</li>
            </ul>
          </div>
        </div>
      </div>
    </NextIntlClientProvider>
  );
};

export default TestChatPage;
